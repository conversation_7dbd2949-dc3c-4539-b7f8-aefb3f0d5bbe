use std::sync::Arc;
use axum::{
    routing::{get},
    extract::{Path, WebSocketUpgrade, State},
    response::IntoResponse,
    Router,
};
use axum::extract::ws::{Message, WebSocket};
use dashmap::DashMap;
use tokio::sync::mpsc;
use serde::Serialize;

type Tx = mpsc::UnboundedSender<String>;
type Clients = Arc<DashMap<String, Tx>>;

#[tokio::main]
async fn main() {
    let clients: Clients = Arc::new(DashMap::new());

    let app = Router::new()
        .route("/start/:task_id", get(start_task))
        .route("/ws/:task_id", get(ws_handler))
        .with_state(clients);

    println!("Listening on http://localhost:3000");
    axum::Server::bind(&"0.0.0.0:3000".parse().unwrap())
        .serve(app.into_make_service())
        .await
        .unwrap();
}


async fn ws_handler(
    ws: WebSocketUpgrade,
    Path(task_id): Path<String>,
    State(clients): State<Clients>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_ws(socket, task_id, clients))
}

async fn handle_ws(mut socket: WebSocket, task_id: String, clients: Clients) {
    let (tx, mut rx) = mpsc::unbounded_channel::<String>();

    // Store sender in map
    clients.insert(task_id.clone(), tx);

    // Push messages to socket
    while let Some(msg) = rx.recv().await {
        if socket.send(Message::Text(msg)).await.is_err() {
            break;
        }
    }

    clients.remove(&task_id);
}

async fn start_task(Path(task_id): Path<String>, State(clients): State<Clients>) -> impl IntoResponse {
    // respond 202
    tokio::spawn(run_long_task(task_id, clients));
    axum::http::StatusCode::ACCEPTED
}

#[derive(Serialize)]
struct Progress {
    step: usize,
    total: usize,
    message: String,
}

async fn run_long_task(task_id: String, clients: Clients) {
    for step in 1..=5 {
        tokio::time::sleep(std::time::Duration::from_secs(1)).await;

        if let Some(sender) = clients.get(&task_id) {
            let progress = Progress {
                step,
                total: 5,
                message: format!("Progress {}%", step * 20),
            };
            let json = serde_json::to_string(&progress).unwrap();
            let _ = sender.send(json);
        }
    }
}
